rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    
    // Check if user is a member of workspace
    function isWorkspaceMember(workspaceId) {
      return isAuthenticated() && 
        exists(/databases/$(database)/documents/workspace_members/$(workspaceId + '_' + request.auth.uid));
    }
    
    // Get workspace member document
    function getWorkspaceMember(workspaceId) {
      return get(/databases/$(database)/documents/workspace_members/$(workspaceId + '_' + request.auth.uid));
    }
    
    // Check if user has specific role in workspace
    function hasWorkspaceRole(workspaceId, roles) {
      return isAuthenticated() && 
        isWorkspaceMember(workspaceId) &&
        getWorkspaceMember(workspaceId).data.role in roles;
    }
    
    function isWorkspaceAdmin(workspaceId) {
      return hasWorkspaceRole(workspaceId, ['owner', 'admin']);
    }
    
    // Test collection - allow authenticated users to read/write their own test documents
    match /test/{document} {
      allow read, write: if request.auth != null;
    }
    
    // Frontend test writes collection - for testing Firebase connectivity
    match /frontend-test-writes/{document} {
      allow read, write: if request.auth != null;
    }
    
    // Leads collection - for storing project idea submissions
    match /leads/{document} {
      // Allow public creation of leads (for chatbot functionality)
      allow create: if true;
      
      // Allow users to read their own leads (using email verification)
      allow read: if request.auth != null 
        && request.auth.token.email == resource.data.contactEmail;
      
      // Allow admin users to read all leads (implement admin check later)
      allow read: if request.auth != null 
        && exists(/databases/$(database)/documents/users/$(request.auth.uid)) 
        && get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
      
      // Prevent updates and deletes for data integrity
      allow update, delete: if false;
    }
    
    // Users collection - stores user authentication data
    match /users/{userId} {
      allow read: if isOwner(userId);
      allow create: if isOwner(userId);
      allow update: if isOwner(userId);
      allow delete: if false; // Soft delete only through update
    }
    
    // Profiles collection - stores user profile data
    match /profiles/{profileId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() && 
        request.resource.data.userId == request.auth.uid;
      allow update: if isAuthenticated() && 
        resource.data.userId == request.auth.uid;
      allow delete: if false; // Soft delete only through status update
    }
    
    // Workspaces collection - stores workspace data
    match /workspaces/{workspaceId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
        request.resource.data.ownerId == request.auth.uid;
      allow update: if isAuthenticated() && 
        resource.data.ownerId == request.auth.uid;
      allow delete: if false; // Soft delete only through update
    }
    
    // Workspace members collection - stores workspace membership data
    match /workspace_members/{memberId} {
      // Allow authenticated users to read workspace memberships
      allow read: if isAuthenticated() && 
        (resource.data.userId == request.auth.uid ||
         isWorkspaceMember(resource.data.workspaceId));
      
      // Allow creation if user is creating their own membership
      // OR if user is workspace admin adding a new member
      allow create: if isAuthenticated() && 
        (request.resource.data.userId == request.auth.uid ||
         isWorkspaceAdmin(request.resource.data.workspaceId));
      
      // Allow updates by workspace admins
      allow update: if isAuthenticated() && 
        isWorkspaceAdmin(resource.data.workspaceId);
      
      // No hard deletes allowed
      allow delete: if false;
    }
    
    // Email queue collection - for storing emails to be sent
    match /emailQueue/{document} {
      // Allow creation of emails from frontend app
      allow create: if true;
      
      // Only allow admins to read/update/delete emails
      allow read, update, delete: if request.auth != null 
        && exists(/databases/$(database)/documents/users/$(request.auth.uid)) 
        && get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // Integrations collection - stores API integrations for LLMs and other services
    match /integrations/{integrationId} {
      // Allow users to read their own integrations
      allow read: if isAuthenticated() && (
        resource.data.ownerId == request.auth.uid ||
        (resource.data.availableToProfiles == true && 
         resource.data.workspaceId != null &&
         isWorkspaceMember(resource.data.workspaceId))
      );
      
      // Allow any authenticated user to create integrations
      // The server will set the correct ownerId and workspaceId
      allow create: if request.auth != null;
      
      // Allow users to update their own integrations
      allow update: if request.auth != null && 
        resource.data.ownerId == request.auth.uid;
      
      // Soft delete only - no hard deletes
      allow delete: if false;
    }
    
    // Default rule - deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
