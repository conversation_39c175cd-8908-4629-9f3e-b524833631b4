<script setup lang="ts">
import type { LLMProvider } from '~/layers/auth-module/types/integration'

interface Props {
  provider: LLMProvider
  modelValue?: string
  showPricing?: boolean
  showContext?: boolean
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showPricing: true,
  showContext: true,
  disabled: false,
})

const emit = defineEmits<{
  'update:modelValue': [value: string]
}>()

// Get models for provider
const { getModels, formatContextWindow, formatPricing, getActiveModels } = useLLMProviders()
const models = computed(() => getActiveModels(props.provider))

// Selected model details
const selectedModel = computed(() =>
  models.value.find(m => m.id === props.modelValue),
)

// Handle selection
function handleSelect(modelId: string) {
  emit('update:modelValue', modelId)
}
</script>

<template>
  <BaseListbox
    :model-value="modelValue"
    :disabled="disabled"
    @update:model-value="handleSelect"
  >
    <BaseListboxButton shape="curved">
      <span v-if="selectedModel">
        {{ selectedModel.displayName }}
      </span>
      <span v-else class="text-muted-400">
        Select a model
      </span>
    </BaseListboxButton>

    <BaseListboxOptions>
      <BaseListboxOption
        v-for="model in models"
        :key="model.id"
        :value="model.id"
      >
        <div class="flex items-start justify-between gap-3">
          <div class="min-w-0 flex-1">
            <BaseText
              size="sm"
              weight="medium"
              class="text-muted-800 dark:text-muted-200"
            >
              {{ model.displayName }}
            </BaseText>

            <div class="mt-1 flex flex-wrap items-center gap-3">
              <!-- Context window -->
              <div
                v-if="showContext && model.contextWindow"
                class="flex items-center gap-1"
              >
                <Icon
                  name="ph:text-columns"
                  class="size-3 text-muted-400"
                />
                <BaseText
                  size="xs"
                  class="text-muted-500 dark:text-muted-400"
                >
                  {{ formatContextWindow(model.contextWindow) }}
                </BaseText>
              </div>

              <!-- Pricing -->
              <div
                v-if="showPricing && model.pricing"
                class="flex items-center gap-1"
              >
                <Icon
                  name="ph:currency-dollar"
                  class="size-3 text-muted-400"
                />
                <BaseText
                  size="xs"
                  class="text-muted-500 dark:text-muted-400"
                >
                  {{ formatPricing(model) }}
                </BaseText>
              </div>

              <!-- Knowledge cutoff -->
              <div
                v-if="model.knowledgeCutoff"
                class="flex items-center gap-1"
              >
                <Icon
                  name="ph:calendar"
                  class="size-3 text-muted-400"
                />
                <BaseText
                  size="xs"
                  class="text-muted-500 dark:text-muted-400"
                >
                  {{ model.knowledgeCutoff }}
                </BaseText>
              </div>
            </div>

            <!-- Capabilities -->
            <div
              v-if="model.capabilities.length > 0"
              class="mt-2 flex flex-wrap gap-1"
            >
              <BaseTag
                v-for="capability in model.capabilities"
                :key="capability"
                size="xs"
                variant="soft"
                shape="full"
              >
                {{ capability }}
              </BaseTag>
            </div>
          </div>

          <!-- Selected indicator -->
          <Icon
            v-if="model.id === modelValue"
            name="ph:check-circle-fill"
            class="size-5 text-primary-500 shrink-0"
          />
        </div>
      </BaseListboxOption>
    </BaseListboxOptions>
  </BaseListbox>
</template>
