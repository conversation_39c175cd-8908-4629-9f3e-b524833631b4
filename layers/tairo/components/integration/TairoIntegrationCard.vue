<script setup lang="ts">
import type { Integration } from '~/layers/auth-module/types/integration'

interface Props {
  integration: Integration
  showInheritance?: boolean
  inheritanceStatus?: {
    isProfileSpecific: boolean
    isInherited: boolean
    isOverridden: boolean
    isEffective: boolean
    source: 'profile' | 'user'
  }
}

const props = withDefaults(defineProps<Props>(), {
  showInheritance: false,
})

const emit = defineEmits<{
  toggle: [value: boolean]
  edit: []
  delete: []
  setDefault: []
}>()

// Get provider config
const { getProvider, getProviderIcon, formatContextWindow } = useLLMProviders()
const provider = computed(() => getProvider(props.integration.provider))

// Get default model info
const defaultModel = computed(() => {
  if (!provider.value || !props.integration.settings.defaultModel)
    return null
  return provider.value.models.find(m => m.id === props.integration.settings.defaultModel)
})

// Handle toggle
function handleToggle(value: boolean) {
  emit('toggle', value)
}
</script>

<template>
  <div class="group relative">
    <!-- Inheritance indicator -->
    <div
      v-if="showInheritance && inheritanceStatus?.isInherited"
      class="absolute -top-2 -left-2 z-10"
    >
      <BaseTooltip
        :content="inheritanceStatus.isOverridden ? 'Inherited from user (overridden)' : 'Inherited from user'"
      >
        <div class="bg-primary-500/10 text-primary-600 dark:bg-primary-500/20 dark:text-primary-400 rounded-full p-1">
          <Icon
            :name="inheritanceStatus.isOverridden ? 'ph:link-break' : 'ph:link'"
            class="size-3"
          />
        </div>
      </BaseTooltip>
    </div>

    <div
      class="relative rounded-xl border transition-all duration-200" :class="[
        integration.isActive
          ? 'border-success-500/20 bg-success-50/50 dark:bg-success-950/20'
          : 'border-muted-200 bg-white dark:border-muted-800 dark:bg-muted-950',
        inheritanceStatus?.isOverridden ? 'opacity-60' : '',
      ]"
    >
      <div class="p-5">
        <div class="flex items-start justify-between gap-4">
          <!-- Provider info -->
          <div class="flex items-start gap-4 flex-1">
            <div class="shrink-0">
              <div
                class="flex size-12 items-center justify-center rounded-xl border" :class="[
                  integration.isActive
                    ? 'border-success-500/20 bg-success-500/10'
                    : 'border-muted-200 bg-muted-50 dark:border-muted-800 dark:bg-muted-900',
                ]"
              >
                <Icon
                  :name="getProviderIcon(integration.provider)"
                  class="size-6"
                  :class="[
                    integration.isActive
                      ? 'text-success-600 dark:text-success-400'
                      : 'text-muted-600 dark:text-muted-400',
                  ]"
                />
              </div>
            </div>

            <div class="min-w-0 flex-1">
              <div class="flex items-center gap-2">
                <BaseHeading
                  as="h4"
                  size="sm"
                  weight="medium"
                  class="text-muted-900 dark:text-white"
                >
                  {{ integration.name }}
                </BaseHeading>
                <BaseTag
                  v-if="integration.isDefault"
                  size="sm"
                  variant="primary"
                  shape="full"
                >
                  Default
                </BaseTag>
              </div>

              <BaseParagraph
                v-if="integration.description"
                size="xs"
                class="text-muted-500 dark:text-muted-400 mt-1"
              >
                {{ integration.description }}
              </BaseParagraph>

              <div class="mt-2 flex flex-wrap items-center gap-4 text-xs">
                <div class="flex items-center gap-1.5">
                  <Icon
                    name="ph:robot"
                    class="size-4 text-muted-400"
                  />
                  <span class="text-muted-600 dark:text-muted-400">
                    {{ defaultModel?.displayName || 'No model selected' }}
                  </span>
                </div>

                <div
                  v-if="defaultModel"
                  class="flex items-center gap-1.5"
                >
                  <Icon
                    name="ph:text-columns"
                    class="size-4 text-muted-400"
                  />
                  <span class="text-muted-600 dark:text-muted-400">
                    {{ formatContextWindow(defaultModel.contextWindow) }} context
                  </span>
                </div>

                <div
                  v-if="integration.lastUsedAt"
                  class="flex items-center gap-1.5"
                >
                  <Icon
                    name="ph:clock"
                    class="size-4 text-muted-400"
                  />
                  <span class="text-muted-600 dark:text-muted-400">
                    Last used {{ useTimeAgo(integration.lastUsedAt.toDate()).value }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- Actions -->
          <div class="flex items-center gap-2">
            <!-- Edit button -->
            <BaseButtonIcon
              size="sm"
              shape="full"
              data-tooltip="Edit integration"
              @click="emit('edit')"
            >
              <Icon name="ph:pencil" />
            </BaseButtonIcon>

            <!-- Delete button -->
            <BaseButtonIcon
              size="sm"
              shape="full"
              color="danger"
              data-tooltip="Delete integration"
              @click="emit('delete')"
            >
              <Icon name="ph:trash" />
            </BaseButtonIcon>

            <!-- Toggle switch -->
            <BaseSwitchBall
              :model-value="integration.isActive"
              @update:model-value="handleToggle"
            />
          </div>
        </div>

        <!-- Additional actions -->
        <div
          v-if="integration.isActive && !integration.isDefault"
          class="mt-4 pt-4 border-t border-muted-200 dark:border-muted-800"
        >
          <BaseButton
            size="sm"
            variant="soft"
            @click="emit('setDefault')"
          >
            <Icon name="ph:star" class="size-4" />
            <span>Set as default</span>
          </BaseButton>
        </div>
      </div>
    </div>
  </div>
</template>
