<script setup lang="ts">
definePageMeta({
  title: 'Integrations',
  preview: {
    title: 'Preferences - Integrations',
    description: 'For account management',
    categories: ['layouts', 'settings'],
    src: '/img/screens/layouts-preferences-integrations.png',
    srcDark: '/img/screens/layouts-preferences-integrations-dark.png',
    order: 87,
    new: true,
  },
})

// Initialize API and toaster
const integrationsApi = useIntegrationsApi()
const toaster = useToast()

const integrations = [
  {
    name: 'Mailchimp',
    description: 'Lorem ipsum dolor sit amet, consectetur adipis.',
    icon: 'logos:mailchimp-freddie',
  },
  {
    name: 'Zapier',
    description: 'Connect with Zapier API.',
    icon: 'logos:zapier-icon',
    live: true,
  },
  {
    name: 'Telegram',
    description: 'Lorem ipsum dolor sit amet.',
    icon: 'logos:telegram',
  },
  {
    name: 'Slack',
    description: 'Lorem ipsum dolor sit amet, consectetur adipis.',
    icon: 'logos:slack-icon',
  },
  {
    name: 'Dropbox',
    description: 'Lorem ipsum dolor sit amet adipis.',
    icon: 'logos:dropbox',
  },
  {
    name: 'OpenAI',
    description: 'Connect to OpenAI API.',
    icon: 'logos:openai',
    live: true,
  },
  {
    name: 'Grok',
    description: 'Connect to Grok AI API.',
    icon: 'logos:grok-ai',
    live: true,
  },
  {
    name: 'Claude',
    description: 'Connect to Claude AI API.',
    icon: 'logos:claude-ai',
    live: true,
  },
  {
    name: 'Stripe',
    description: 'Connect to Stripe for payment processing.',
    icon: 'logos:stripe-icon',
    live: true,
  },
  {
    name: 'Paypal',
    description: 'Connect to Paypal for payment processing.',
    icon: 'logos:paypal-icon',
    live: true,
  },
]

// Reactive data for modal and API keys
const modalVisible = ref({})
const apiKeys = ref({})
const connectedIntegrations = ref([])
const savedIntegrations = ref([])
const isLoading = ref(false)
const isSaving = ref({})

// Load existing integrations on mount
async function loadIntegrations() {
  try {
    isLoading.value = true
    const integrations = await integrationsApi.getAll()
    savedIntegrations.value = integrations

    // Populate connected integrations list
    connectedIntegrations.value = integrations
      .filter(integration => integration.isActive)
      .map(integration => integration.name)
  }
  catch (error) {
    console.error('Failed to load integrations:', error)
  }
  finally {
    isLoading.value = false
  }
}

// Call on mount
onMounted(() => {
  loadIntegrations()
})

// Methods
function showModal(integrationName) {
  modalVisible.value[integrationName] = true
}

function closeModal(integrationName) {
  modalVisible.value[integrationName] = false
}

async function connectIntegration(integrationName) {
  if (apiKeys.value[integrationName]) {
    isSaving.value[integrationName] = true
    try {
      // Map integration names to provider IDs
      const providerMapping = {
        OpenAI: 'openai',
        Claude: 'anthropic',
        Grok: 'xai',
        Mistral: 'mistral',
        Cohere: 'cohere',
        Perplexity: 'perplexity',
        Meta: 'meta',
        NVIDIA: 'nvidia',
        Groq: 'groq',
        Stripe: 'stripe',
        Paypal: 'paypal',
      }

      const providerId = providerMapping[integrationName] || integrationName.toLowerCase()
      
      // Get provider config for LLM providers
      let defaultModel = ''
      if (['openai', 'anthropic', 'xai', 'mistral', 'cohere', 'perplexity', 'meta', 'nvidia', 'groq'].includes(providerId)) {
        const { getLLMProvider, getDefaultModel } = await import('../../config/llm-providers')
        const provider = getLLMProvider(providerId)
        const model = getDefaultModel(providerId)
        if (model) {
          defaultModel = model.id
        }
      }

      const integrationData = {
        provider: providerId,
        name: integrationName,
        description: `${integrationName} integration`,
        credentials: {
          apiKey: apiKeys.value[integrationName],
          encryptedAt: new Date(),
        },
        settings: {
          defaultModel,
        },
        isActive: true,
        isDefault: false,
        availableToProfiles: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      await integrationsApi.create(integrationData)

      if (!connectedIntegrations.value.includes(integrationName)) {
        connectedIntegrations.value.push(integrationName)
      }
      
      // Reload integrations after successful creation
      await loadIntegrations()

      // Show success toast
      toaster.success('Integration connected', `Successfully connected to ${integrationName}`)

      console.log(`Connected to ${integrationName} successfully`)
    }
    catch (error) {
      console.error(`Failed to connect integration ${integrationName}:`, error)
      
      // Show error toast
      toaster.error('Connection failed', `Failed to connect to ${integrationName}. Please check your API key and try again.`)
    }
    finally {
      isSaving.value[integrationName] = false
      closeModal(integrationName)
      // Clear the API key for security
      delete apiKeys.value[integrationName]
    }
  }
}

async function disconnectIntegration(integrationName) {
  try {
    // Find the integration in saved integrations
    const integration = savedIntegrations.value.find(int => int.name === integrationName)

    if (integration) {
      // Soft delete by setting isActive to false
      await integrationsApi.update(integration.id, {
        isActive: false,
        updatedAt: new Date(),
      })
    }

    if (integration) {
      // Soft delete by setting isActive to false
      await integrationsApi.update(integration.id, {
        isActive: false,
        updatedAt: new Date(),
      })
    }

    // Remove from connected integrations
    const index = connectedIntegrations.value.indexOf(integrationName)
    if (index > -1) {
      connectedIntegrations.value.splice(index, 1)
    }

    // Clear the API key
    delete apiKeys.value[integrationName]
    
    // Reload integrations
    await loadIntegrations()
    
    // Show success toast
    toaster.success('Integration disconnected', `Successfully disconnected from ${integrationName}`)

    console.log(`Disconnected from ${integrationName}`)
  }
  catch (error) {
    console.error(`Failed to disconnect integration ${integrationName}:`, error)
    
    // Show error toast
    toaster.error('Disconnection failed', `Failed to disconnect from ${integrationName}. Please try again.`)
  }
}
</script>

<template>
  <div class="mt-8 dark:[--color-input-default-bg:var(--color-muted-950)]">
    <div class="border-primary-500 bg-primary-500/10 rounded-2xl border-2">
      <div class="p-4">
        <div class="gap-6 md:flex md:items-center md:justify-between">
          <BaseAvatar
            rounded="none"
            mask="blob"
            src="/img/avatars/15.svg"
            size="lg"
          />
          <div class="max-w-xs flex-1">
            <BaseParagraph weight="semibold" class="text-primary-700 dark:text-primary-400">
              Learn how to connect to our API
            </BaseParagraph>
            <BaseParagraph
              size="sm"
              class="text-primary-600 dark:text-primary-300"
            >
              We've put together a nice and simple tutorial.
            </BaseParagraph>
          </div>

          <div class="mt-6 flex items-center justify-start gap-3 md:ms-auto md:mt-0 md:justify-end md:space-x-reverse">
            <BaseButton rounded="md">
              Dismiss
            </BaseButton>
            <BaseButton
              variant="primary"
              rounded="md"
            >
              View Tutorial
            </BaseButton>
          </div>
        </div>
      </div>
    </div>

    <div class="mt-8 sm:flex sm:items-center sm:justify-between">
      <div class="space-y-1">
        <BaseHeading
          as="h4"
          size="md"
          weight="medium"
          class="text-muted-900 text-base font-bold dark:text-white"
        >
          Connected integrations
        </BaseHeading>
        <BaseParagraph
          size="sm"
          weight="medium"
          class="text-muted-500 dark:text-muted-400"
        >
          View and manage your connected integrations.
        </BaseParagraph>
      </div>

      <TairoInput
        icon="lucide:search"
        rounded="md"
        placeholder="Search integrations..."
        class="mt-4 sm:mt-0"
      />
    </div>

    <!-- Connected Integrations List -->
    <div v-if="connectedIntegrations.length > 0" class="mt-8 flow-root">
      <div class="divide-muted-200 dark:divide-muted-700 -my-5 divide-y">
        <div
          v-for="integrationName in connectedIntegrations"
          :key="integrationName"
          class="py-5"
        >
          <div class="sm:flex sm:items-center sm:justify-between sm:space-x-5">
            <div class="flex min-w-0 flex-1 items-center">
              <div class="dark:bg-muted-950 border-muted-300 dark:border-muted-800 mb-3 flex size-12 items-center justify-center rounded-xl border bg-white">
                <div class="bg-muted-100 dark:bg-muted-800 flex size-10 items-center justify-center rounded-lg">
                  <Icon name="ph:check-circle" class="text-green-500 size-6" />
                </div>
              </div>
              <div class="ms-4 min-w-0 flex-1 space-y-1">
                <BaseParagraph
                  size="sm"
                  weight="semibold"
                  class="text-muted-900 dark:text-muted-100 truncate"
                >
                  {{ integrationName }}
                </BaseParagraph>
                <BaseParagraph
                  size="xs"
                  weight="medium"
                  class="text-green-600 dark:text-green-400"
                >
                  Connected and active
                </BaseParagraph>
              </div>
            </div>

            <div class="mt-4 flex items-center justify-between ps-14 sm:mt-0 sm:justify-end sm:space-x-6 sm:ps-0">
              <BaseButton size="sm" variant="soft" color="danger" @click="disconnectIntegration(integrationName)">
                Disconnect
              </BaseButton>
              <BaseSwitchBall :model-value="true" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- No Connected Integrations Message -->
    <div v-else class="mt-8">
      <div class="text-center py-12">
        <Icon name="ph:plug" class="mx-auto h-12 w-12 text-muted-400" />
        <BaseHeading as="h3" size="md" weight="medium" class="mt-4 text-muted-900 dark:text-white">
          No connected integrations
        </BaseHeading>
        <BaseParagraph class="mt-2 text-muted-500 dark:text-muted-400">
          Connect to your favorite services below to get started.
        </BaseParagraph>
      </div>
    </div>

    <div class="mt-8 sm:flex sm:items-center sm:justify-between">
      <div class="space-y-1">
        <BaseHeading
          as="h4"
          size="md"
          weight="medium"
          class="text-muted-900 text-base font-bold dark:text-white"
        >
          Available integrations
        </BaseHeading>
        <BaseParagraph
          size="sm"
          weight="medium"
          class="text-muted-500 dark:text-muted-400"
        >
          View and manage your connected integrations.
        </BaseParagraph>
      </div>

      <TairoInput
        icon="lucide:search"
        rounded="md"
        placeholder="Search integrations..."
        class="mt-4 sm:mt-0"
      />
    </div>

    <div>
      <!-- Header -->
      <div class="border-muted-200 dark:border-muted-800 border-b py-6">
        <BaseHeading
          as="h2"
          size="lg"
          weight="medium"
          class="text-muted-800 dark:text-white"
        >
          Accounting
        </BaseHeading>
        <BaseParagraph size="xs" class="text-muted-500 dark:text-muted-400 max-w-sm">
          Available accounting integrations
        </BaseParagraph>
      </div>
      <!-- Content -->
      <div
        class="divide-muted-200 dark:divide-muted-800 space-y-8 divide-y pt-6"
      >
        <!-- Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar
            size="sm"
            src="/img/logos/companies/quickbooks-full.svg"
          />
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              Quickbooks
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              QuickBooks is a user-friendly, simple accounting software that
              tracks your business income and expenses, and organises your
              financial information for you, eliminating manual data entry.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect to Quickbooks</span>
            </BaseButton>
          </div>
        </div>
        <!-- Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar size="sm" src="/img/logos/companies/xero-full.svg" />
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              Xero
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Xero is a user-friendly, simple accounting software that tracks
              your business income and expenses, and organises your financial
              information for you, eliminating manual data entry.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect to Xero</span>
            </BaseButton>
          </div>
        </div>
        <!-- Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar
            size="sm"
            src="/img/logos/companies/freshbooks-full.svg"
          />
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              Freshbooks
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Freshbooks is a user-friendly, simple accounting software that
              tracks your business income and expenses, and organises your
              financial information for you, eliminating manual data entry.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect to Freshbooks</span>
            </BaseButton>
          </div>
        </div>
      </div>
    </div>

    <!-- AI/LLM Group -->
    <div>
      <!-- Header -->
      <div class="border-muted-200 dark:border-muted-800 border-b py-6">
        <BaseHeading
          as="h2"
          size="lg"
          weight="medium"
          class="text-muted-800 dark:text-white"
        >
          AI & Language Models
        </BaseHeading>
        <BaseParagraph size="xs" class="text-muted-500 dark:text-muted-400 max-w-sm">
          Connect to powerful AI and language model providers
        </BaseParagraph>
      </div>
      <!-- Content -->
      <div
        class="divide-muted-200 dark:divide-muted-800 divide-y pt-6"
      >
        <!-- OpenAI Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar size="sm" src="/img/logos/companies/openai-full.svg" />
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              OpenAI
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Connect to OpenAI's powerful language models including GPT-4, GPT-3.5,
              and DALL-E for advanced AI capabilities in your applications.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
              @click="showModal('OpenAI')"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect to OpenAI</span>
            </BaseButton>
          </div>
        </div>

        <!-- Grok Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar size="sm" src="/img/logos/companies/grok-full.svg" />
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              Grok
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Connect to Grok AI by xAI for advanced language processing and
              real-time information access with conversational AI capabilities.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
              @click="showModal('Grok')"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect to Grok</span>
            </BaseButton>
          </div>
        </div>

        <!-- Claude Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar size="sm" src="/img/logos/companies/claude-full.svg" />
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              Claude
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Connect to Anthropic's Claude AI for safe, helpful, and honest AI
              assistance with advanced reasoning and analysis capabilities.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
              @click="showModal('Claude')"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect to Claude</span>
            </BaseButton>
          </div>
        </div>

        <!-- Mistral AI Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar size="sm" src="/img/logos/companies/mistral-full.svg" />
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              Mistral AI
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Connect to Mistral AI for advanced language models with excellent reasoning
              capabilities, supporting French and English with high performance.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
              @click="showModal('Mistral')"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect to Mistral</span>
            </BaseButton>
          </div>
        </div>

        <!-- Cohere AI Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar size="sm" src="/img/logos/companies/cohere-full.svg" />
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              Cohere AI
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Connect to Cohere AI for enterprise-grade NLP models optimized for
              business applications, semantic search, and text generation.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
              @click="showModal('Cohere')"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect to Cohere</span>
            </BaseButton>
          </div>
        </div>

        <!-- Perplexity AI Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar size="sm" src="/img/logos/companies/perplexity-full.svg" />
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              Perplexity AI
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Connect to Perplexity AI for real-time information access and advanced
              question answering with up-to-date search capabilities.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
              @click="showModal('Perplexity')"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect to Perplexity</span>
            </BaseButton>
          </div>
        </div>

        <!-- Meta AI Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar size="sm" src="/img/logos/companies/meta-full.svg" />
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              Meta AI
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Connect to Meta AI's Llama models for open-source language understanding
              and generation with strong multilingual support.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
              @click="showModal('Meta')"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect to Meta</span>
            </BaseButton>
          </div>
        </div>

        <!-- NVIDIA AI Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar size="sm" src="/img/logos/companies/nvidia-full.svg" />
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              NVIDIA AI
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Connect to NVIDIA AI for high-performance computing models optimized
              for GPU acceleration and enterprise-scale AI applications.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
              @click="showModal('NVIDIA')"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect to NVIDIA</span>
            </BaseButton>
          </div>
        </div>

        <!-- Groq AI Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar size="sm" src="/img/logos/companies/groq-full.svg" />
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              Groq AI
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Connect to Groq AI for ultra-fast inference speeds with their custom
              LPU technology, ideal for real-time AI applications.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
              @click="showModal('Groq')"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect to Groq</span>
            </BaseButton>
          </div>
        </div>
      </div>
    </div>

    <!-- Group -->
    <div>
      <!-- Header -->
      <div class="border-muted-200 dark:border-muted-800 border-b py-6">
      <BaseHeading
        as="h2"
        size="lg"
        weight="medium"
        class="text-muted-800 dark:text-white"
      >
        Other
      </BaseHeading>
      <BaseParagraph size="xs" class="text-muted-500 dark:text-muted-400 max-w-sm">
        Other available integrations
      </BaseParagraph>
    </div>
    <!-- Content -->
    <div
      class="divide-muted-200 dark:divide-muted-800 divide-y pt-6"
    >
      <!-- Item -->
      <div class="flex flex-col gap-3 py-6 lg:flex-row">
        <BaseAvatar size="sm" src="/img/logos/companies/zapier-full.svg" />
        <div class="max-w-lg">
          <BaseHeading
            as="h4"
            size="lg"
            weight="medium"
            lead="tight"
            class="text-muted-800 mb-1 dark:text-white"
          >
            Zapier
          </BaseHeading>
          <BaseParagraph
            size="xs"
            class="text-muted-500 dark:text-muted-400 max-w-sm"
          >
            Zapier is a user-friendly, simple accounting software that
            tracks your business income and expenses, and organises your
            financial information for you, eliminating manual data entry.
          </BaseParagraph>
        </div>
        <div class="lg:ms-auto">
          <BaseButton
            rounded="md"
            size="sm"
          >
            <Icon name="solar:link-circle-linear" class="size-4" />
            <span>Connect to Zapier</span>
          </BaseButton>
        </div>
      </div>
      <!-- Item -->
      <div class="flex flex-col gap-3 py-6 lg:flex-row">
        <BaseAvatar size="sm" src="/img/logos/companies/google-full.svg" />
        <div class="max-w-lg">
          <BaseHeading
            as="h4"
            size="lg"
            weight="medium"
            lead="tight"
            class="text-muted-800 mb-1 dark:text-white"
          >
            Google Suite
          </BaseHeading>
          <BaseParagraph
            size="xs"
            class="text-muted-500 dark:text-muted-400 max-w-sm"
          >
            Google is a user-friendly, simple accounting software that
            tracks your business income and expenses, and organises your
            financial information for you, eliminating manual data entry.
          </BaseParagraph>
        </div>
        <div class="lg:ms-auto">
          <BaseButton
            rounded="md"
            size="sm"
          >
            <Icon name="solar:link-circle-linear" class="size-4" />
            <span>Connect to Google</span>
          </BaseButton>
        </div>
      </div>
      <!-- Item -->
      <div class="flex flex-col gap-3 py-6 lg:flex-row">
        <BaseAvatar size="sm" src="/img/logos/companies/stripe-full.svg" />
        <div class="max-w-lg">
          <BaseHeading
            as="h4"
            size="lg"
            weight="medium"
            lead="tight"
            class="text-muted-800 mb-1 dark:text-white"
          >
            Stripe
          </BaseHeading>
          <BaseParagraph
            size="xs"
            class="text-muted-500 dark:text-muted-400 max-w-sm"
          >
            Stripe payment processing integration for secure online transactions.
            Accept credit cards and digital payments with Stripe's powerful platform.
          </BaseParagraph>
        </div>
        <div class="lg:ms-auto">
          <BaseButton
            rounded="md"
            size="sm"
            @click="showModal('Stripe')"
          >
            <Icon name="solar:link-circle-linear" class="size-4" />
            <span>Connect to Stripe</span>
          </BaseButton>
        </div>
      </div>
      <!-- Item -->
      <div class="flex flex-col gap-3 py-6 lg:flex-row">
        <BaseAvatar size="sm" src="/img/logos/companies/paypal-full.svg" />
        <div class="max-w-lg">
          <BaseHeading
            as="h4"
            size="lg"
            weight="medium"
            lead="tight"
            class="text-muted-800 mb-1 dark:text-white"
          >
            Paypal
          </BaseHeading>
          <BaseParagraph
            size="xs"
            class="text-muted-500 dark:text-muted-400 max-w-sm"
          >
            Paypal payment processing integration for secure online transactions.
            Accept payments from customers worldwide with PayPal's trusted platform.
          </BaseParagraph>
        </div>
        <div class="lg:ms-auto">
          <BaseButton
            rounded="md"
            size="sm"
            @click="showModal('Paypal')"
          >
            <Icon name="solar:link-circle-linear" class="size-4" />
            <span>Connect to Paypal</span>
          </BaseButton>
        </div>
      </div>
    </div>
    </div>
  </div>

  <!-- Global Modal Components -->
  <TairoModal :open="modalVisible.OpenAI" size="sm" @close="closeModal('OpenAI')">
    <template #header>
      <BaseHeading as="h3" size="md" weight="medium">
        Connect to OpenAI
      </BaseHeading>
    </template>

    <div class="space-y-4">
      <BaseInput v-model="apiKeys.OpenAI" placeholder="Enter OpenAI API Key" type="password" label="API Key" />
      <BaseParagraph size="xs" class="text-muted-500">
        Your API key is securely stored and encrypted. You can find your API key in your OpenAI dashboard.
      </BaseParagraph>

      <div class="flex gap-2 pt-4">
        <BaseButton 
          color="primary"
          :loading="isSaving.OpenAI"
          :disabled="!apiKeys.OpenAI || isSaving.OpenAI"
          @click="connectIntegration('OpenAI')"
        >
          <span v-if="!isSaving.OpenAI">Connect</span>
          <span v-else>Connecting...</span>
        </BaseButton>
        <BaseButton variant="ghost" @click="closeModal('OpenAI')">
          Cancel
        </BaseButton>
      </div>
    </div>
  </TairoModal>

  <TairoModal :open="modalVisible.Grok" size="sm" @close="closeModal('Grok')">
    <template #header>
      <BaseHeading as="h3" size="md" weight="medium">
        Connect to Grok
      </BaseHeading>
    </template>

    <div class="space-y-4">
      <BaseInput v-model="apiKeys.Grok" placeholder="Enter Grok API Key" type="password" label="API Key" />
      <BaseParagraph size="xs" class="text-muted-500">
        Your API key is securely stored and encrypted. You can find your API key in your xAI dashboard.
      </BaseParagraph>

      <div class="flex gap-2 pt-4">
        <BaseButton 
          color="primary"
          :loading="isSaving.Grok"
          :disabled="!apiKeys.Grok || isSaving.Grok"
          @click="connectIntegration('Grok')"
        >
          <span v-if="!isSaving.Grok">Connect</span>
          <span v-else>Connecting...</span>
        </BaseButton>
        <BaseButton variant="ghost" @click="closeModal('Grok')">
          Cancel
        </BaseButton>
      </div>
    </div>
  </TairoModal>

  <TairoModal :open="modalVisible.Claude" size="sm" @close="closeModal('Claude')">
    <template #header>
      <BaseHeading as="h3" size="md" weight="medium">
        Connect to Claude
      </BaseHeading>
    </template>

    <div class="space-y-4">
      <BaseInput v-model="apiKeys.Claude" placeholder="Enter Claude API Key" type="password" label="API Key" />
      <BaseParagraph size="xs" class="text-muted-500">
        Your API key is securely stored and encrypted. You can find your API key in your Anthropic dashboard.
      </BaseParagraph>

      <div class="flex gap-2 pt-4">
        <BaseButton 
          color="primary"
          :loading="isSaving.Claude"
          :disabled="!apiKeys.Claude || isSaving.Claude"
          @click="connectIntegration('Claude')"
        >
          <span v-if="!isSaving.Claude">Connect</span>
          <span v-else>Connecting...</span>
        </BaseButton>
        <BaseButton variant="ghost" @click="closeModal('Claude')">
          Cancel
        </BaseButton>
      </div>
    </div>
  </TairoModal>

  <TairoModal :open="modalVisible.Stripe" size="sm" @close="closeModal('Stripe')">
    <template #header>
      <BaseHeading as="h3" size="md" weight="medium">
        Connect to Stripe
      </BaseHeading>
    </template>

    <div class="space-y-4">
      <BaseInput v-model="apiKeys.Stripe" placeholder="Enter Stripe API Key" type="password" label="API Key" />
      <BaseParagraph size="xs" class="text-muted-500">
        Your API key is securely stored and encrypted. You can find your API key in your Stripe dashboard.
      </BaseParagraph>

      <div class="flex gap-2 pt-4">
        <BaseButton 
          color="primary"
          :loading="isSaving.Stripe"
          :disabled="!apiKeys.Stripe || isSaving.Stripe"
          @click="connectIntegration('Stripe')"
        >
          <span v-if="!isSaving.Stripe">Connect</span>
          <span v-else>Connecting...</span>
        </BaseButton>
        <BaseButton variant="ghost" @click="closeModal('Stripe')">
          Cancel
        </BaseButton>
      </div>
    </div>
  </TairoModal>

  <TairoModal :open="modalVisible.Paypal" size="sm" @close="closeModal('Paypal')">
    <template #header>
      <BaseHeading as="h3" size="md" weight="medium">
        Connect to Paypal
      </BaseHeading>
    </template>

    <div class="space-y-4">
      <BaseInput v-model="apiKeys.Paypal" placeholder="Enter Paypal API Key" type="password" label="API Key" />
      <BaseParagraph size="xs" class="text-muted-500">
        Your API key is securely stored and encrypted. You can find your API key in your PayPal dashboard.
      </BaseParagraph>

      <div class="flex gap-2 pt-4">
        <BaseButton 
          color="primary"
          :loading="isSaving.Paypal"
          :disabled="!apiKeys.Paypal || isSaving.Paypal"
          @click="connectIntegration('Paypal')"
        >
          <span v-if="!isSaving.Paypal">Connect</span>
          <span v-else>Connecting...</span>
        </BaseButton>
        <BaseButton variant="ghost" @click="closeModal('Paypal')">
          Cancel
        </BaseButton>
      </div>
    </div>
  </TairoModal>

  <TairoModal :open="modalVisible.Mistral" size="sm" @close="closeModal('Mistral')">
    <template #header>
      <BaseHeading as="h3" size="md" weight="medium">
        Connect to Mistral AI
      </BaseHeading>
    </template>

    <div class="space-y-4">
      <BaseInput v-model="apiKeys.Mistral" placeholder="Enter Mistral API Key" type="password" label="API Key" />
      <BaseParagraph size="xs" class="text-muted-500">
        Your API key is securely stored and encrypted. You can find your API key in your Mistral AI dashboard.
      </BaseParagraph>

      <div class="flex gap-2 pt-4">
        <BaseButton 
          color="primary"
          :loading="isSaving.Mistral"
          :disabled="!apiKeys.Mistral || isSaving.Mistral"
          @click="connectIntegration('Mistral')"
        >
          <span v-if="!isSaving.Mistral">Connect</span>
          <span v-else>Connecting...</span>
        </BaseButton>
        <BaseButton variant="ghost" @click="closeModal('Mistral')">
          Cancel
        </BaseButton>
      </div>
    </div>
  </TairoModal>

  <TairoModal :open="modalVisible.Cohere" size="sm" @close="closeModal('Cohere')">
    <template #header>
      <BaseHeading as="h3" size="md" weight="medium">
        Connect to Cohere AI
      </BaseHeading>
    </template>

    <div class="space-y-4">
      <BaseInput v-model="apiKeys.Cohere" placeholder="Enter Cohere API Key" type="password" label="API Key" />
      <BaseParagraph size="xs" class="text-muted-500">
        Your API key is securely stored and encrypted. You can find your API key in your Cohere dashboard.
      </BaseParagraph>

      <div class="flex gap-2 pt-4">
        <BaseButton 
          color="primary"
          :loading="isSaving.Cohere"
          :disabled="!apiKeys.Cohere || isSaving.Cohere"
          @click="connectIntegration('Cohere')"
        >
          <span v-if="!isSaving.Cohere">Connect</span>
          <span v-else>Connecting...</span>
        </BaseButton>
        <BaseButton variant="ghost" @click="closeModal('Cohere')">
          Cancel
        </BaseButton>
      </div>
    </div>
  </TairoModal>

  <TairoModal :open="modalVisible.Perplexity" size="sm" @close="closeModal('Perplexity')">
    <template #header>
      <BaseHeading as="h3" size="md" weight="medium">
        Connect to Perplexity AI
      </BaseHeading>
    </template>

    <div class="space-y-4">
      <BaseInput v-model="apiKeys.Perplexity" placeholder="Enter Perplexity API Key" type="password" label="API Key" />
      <BaseParagraph size="xs" class="text-muted-500">
        Your API key is securely stored and encrypted. You can find your API key in your Perplexity dashboard.
      </BaseParagraph>

      <div class="flex gap-2 pt-4">
        <BaseButton 
          color="primary"
          :loading="isSaving.Perplexity"
          :disabled="!apiKeys.Perplexity || isSaving.Perplexity"
          @click="connectIntegration('Perplexity')"
        >
          <span v-if="!isSaving.Perplexity">Connect</span>
          <span v-else>Connecting...</span>
        </BaseButton>
        <BaseButton variant="ghost" @click="closeModal('Perplexity')">
          Cancel
        </BaseButton>
      </div>
    </div>
  </TairoModal>

  <TairoModal :open="modalVisible.Meta" size="sm" @close="closeModal('Meta')">
    <template #header>
      <BaseHeading as="h3" size="md" weight="medium">
        Connect to Meta AI
      </BaseHeading>
    </template>

    <div class="space-y-4">
      <BaseInput v-model="apiKeys.Meta" placeholder="Enter Meta API Key" type="password" label="API Key" />
      <BaseParagraph size="xs" class="text-muted-500">
        Your API key is securely stored and encrypted. You can find your API key in your Meta AI dashboard.
      </BaseParagraph>

      <div class="flex gap-2 pt-4">
        <BaseButton 
          color="primary"
          :loading="isSaving.Meta"
          :disabled="!apiKeys.Meta || isSaving.Meta"
          @click="connectIntegration('Meta')"
        >
          <span v-if="!isSaving.Meta">Connect</span>
          <span v-else>Connecting...</span>
        </BaseButton>
        <BaseButton variant="ghost" @click="closeModal('Meta')">
          Cancel
        </BaseButton>
      </div>
    </div>
  </TairoModal>

  <TairoModal :open="modalVisible.NVIDIA" size="sm" @close="closeModal('NVIDIA')">
    <template #header>
      <BaseHeading as="h3" size="md" weight="medium">
        Connect to NVIDIA AI
      </BaseHeading>
    </template>

    <div class="space-y-4">
      <BaseInput v-model="apiKeys.NVIDIA" placeholder="Enter NVIDIA API Key" type="password" label="API Key" />
      <BaseParagraph size="xs" class="text-muted-500">
        Your API key is securely stored and encrypted. You can find your API key in your NVIDIA dashboard.
      </BaseParagraph>

      <div class="flex gap-2 pt-4">
        <BaseButton 
          color="primary"
          :loading="isSaving.NVIDIA"
          :disabled="!apiKeys.NVIDIA || isSaving.NVIDIA"
          @click="connectIntegration('NVIDIA')"
        >
          <span v-if="!isSaving.NVIDIA">Connect</span>
          <span v-else>Connecting...</span>
        </BaseButton>
        <BaseButton variant="ghost" @click="closeModal('NVIDIA')">
          Cancel
        </BaseButton>
      </div>
    </div>
  </TairoModal>

  <TairoModal :open="modalVisible.Groq" size="sm" @close="closeModal('Groq')">
    <template #header>
      <BaseHeading as="h3" size="md" weight="medium">
        Connect to Groq AI
      </BaseHeading>
    </template>

    <div class="space-y-4">
      <BaseInput v-model="apiKeys.Groq" placeholder="Enter Groq API Key" type="password" label="API Key" />
      <BaseParagraph size="xs" class="text-muted-500">
        Your API key is securely stored and encrypted. You can find your API key in your Groq dashboard.
      </BaseParagraph>

      <div class="flex gap-2 pt-4">
        <BaseButton 
          color="primary"
          :loading="isSaving.Groq"
          :disabled="!apiKeys.Groq || isSaving.Groq"
          @click="connectIntegration('Groq')"
        >
          <span v-if="!isSaving.Groq">Connect</span>
          <span v-else>Connecting...</span>
        </BaseButton>
        <BaseButton variant="ghost" @click="closeModal('Groq')">
          Cancel
        </BaseButton>
      </div>
    </div>
  </TairoModal>
</template>
