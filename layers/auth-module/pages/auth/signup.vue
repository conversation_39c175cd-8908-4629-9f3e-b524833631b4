<script setup lang="ts">
import { AddonInputPassword } from '#components'
import { toTypedSchema } from '@vee-validate/zod'
import { Field, useForm } from 'vee-validate'

import { z } from 'zod'

definePageMeta({
  layout: 'empty',
  title: 'Signup',
  preview: {
    title: 'Signup 1',
    description: 'For authentication and sign up',
    categories: ['layouts', 'authentication'],
    src: '/img/screens/auth-signup-1.png',
    srcDark: '/img/screens/auth-signup-1-dark.png',
    order: 157,
  },
})

const passwordRef = ref<InstanceType<typeof AddonInputPassword>>()

const VALIDATION_TEXT = {
  EMAIL_REQUIRED: 'A valid email is required',
  USERNAME_LENGTH: 'Username must be at least 3 characters',
  PASSWORD_LENGTH: 'Password must be at least 8 characters',
  PASSWORD_CONTAINS_EMAIL: 'Password cannot contain your email',
  PASSWORD_MATCH: 'Passwords do not match',
}

// This is the Zod schema for the form input
// It's used to define the shape that the form data will have
const zodSchema = z
  .object({
    username: z.string().min(3, VALIDATION_TEXT.USERNAME_LENGTH),
    email: z.string().email(VALIDATION_TEXT.EMAIL_REQUIRED),
    password: z.string().min(8, VALIDATION_TEXT.PASSWORD_LENGTH),
    confirmPassword: z.string(),
  })
  .superRefine((data, ctx) => {
    // This is a custom validation function that will be called
    // before the form is submitted
    if (
      passwordRef.value?.validation?.feedback?.warning
      || passwordRef.value?.validation?.feedback?.suggestions?.length
    ) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message:
            passwordRef.value?.validation?.feedback?.warning
            || passwordRef.value.validation.feedback?.suggestions?.[0],
        path: ['password'],
      })
    }
    if (data.password !== data.confirmPassword) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: VALIDATION_TEXT.PASSWORD_MATCH,
        path: ['confirmPassword'],
      })
    }
  })

  // Zod has a great infer method that will
  // infer the shape of the schema into a TypeScript type
  type FormInput = z.infer<typeof zodSchema>

const validationSchema = toTypedSchema(zodSchema)
const initialValues = {
  username: 'maya',
  email: '',
  password: '',
  confirmPassword: '',
} satisfies FormInput

const { values, handleSubmit, isSubmitting, setFieldError } = useForm({
  validationSchema,
  initialValues,
})

const router = useRouter()
const toaster = useNuiToasts()
const { signup, loginWithGoogle } = useAuth()

// This is where you would send the form data to the server
const onSubmit = handleSubmit(async (values) => {
  console.log('[Signup] Form submitted with values:', { email: values.email, username: values.username })

  try {
    const result = await signup(values)
    console.log('[Signup] Signup successful:', result)

    toaster.add({
      title: 'Success',
      message: `Account created for ${values.username}`,
      color: 'success',
      icon: 'ph:user-circle-fill',
      closable: true,
    })

    // Redirect to dashboard
    console.log('[Signup] Redirecting to dashboard...')
    await router.push('/dashboard')
  }
  catch (error: any) {
    console.error('❌ [Signup] Form submission error:', error)
    const message = error?.data?.message || error.message || 'Something went wrong'

    if (message.includes('username')) {
      setFieldError('username', message)
    }
    else if (message.includes('email')) {
      setFieldError('email', message)
    }
    else {
      toaster.add({
        title: 'Error',
        message,
        color: 'danger',
        icon: 'ph:x-circle-fill',
        closable: true,
      })
    }
  }
})

// Handle Google signup
async function handleGoogleSignup() {
  try {
    await loginWithGoogle()

    // Redirect to dashboards after successful signup/login
    router.push('/dashboard')
  }
  catch (error: any) {
    console.error('Google signup error:', error)
    toaster.add({
      title: 'Error',
      message: error.message || 'Failed to sign up with Google',
      color: 'danger',
      icon: 'ph:x-circle-fill',
      closable: true,
    })
  }
}
</script>

<template>
  <div class="dark:bg-muted-800 flex min-h-screen bg-white">
    <div
      class="relative flex flex-1 flex-col justify-center px-6 py-12 w-1/2 lg:flex-none"
    >
      <div class="dark:bg-muted-800 relative mx-auto w-full max-w-sm bg-white">
        <!-- Nav -->
        <div class="flex w-full items-center justify-between">
          <NuxtLink
            to="/dashboard"
            class="text-muted-400 hover:text-primary-500 flex items-center gap-2 font-sans font-medium transition-colors duration-300"
          >
            <Icon name="gg:arrow-long-left" class="size-5" />
            <span>Back to Home</span>
          </NuxtLink>
          <!-- Theme toggle would go here if available -->
        </div>
        <div>
          <BaseHeading
            as="h2"
            size="3xl"
            lead="relaxed"
            weight="medium"
            class="mt-6"
          >
            Create your account
          </BaseHeading>
          <BaseParagraph size="sm" class="text-muted-400 mb-6">
            Sign up with social media or your details
          </BaseParagraph>
          <!-- Social Sign Up Buttons -->
          <div class="flex flex-wrap justify-between gap-4">
            <!-- Google button -->
            <button
              class="dark:bg-muted-700 text-muted-800 border-muted-300 dark:border-muted-600 nui-focus relative inline-flex grow items-center justify-center gap-2 rounded border bg-white px-6 py-4 dark:text-white"
              @click="handleGoogleSignup"
            >
              <Icon name="logos:google-icon" class="size-5" />
              <div>Sign up with Google</div>
            </button>
            <!-- Twitter button -->
            <button
              class="bg-muted-200 dark:bg-muted-700 hover:bg-muted-100 dark:hover:bg-muted-600 text-muted-600 dark:text-muted-400 nui-focus w-[calc(50%_-_0.5rem)] cursor-pointer rounded px-5 py-4 text-center transition-colors duration-300 md:w-auto"
            >
              <Icon name="fa6-brands:twitter" class="mx-auto size-4" />
            </button>
            <!-- Linkedin button -->
            <button
              class="bg-muted-200 dark:bg-muted-700 hover:bg-muted-100 dark:hover:bg-muted-600 text-muted-600 dark:text-muted-400 nui-focus w-[calc(50%_-_0.5rem)] cursor-pointer rounded px-5 py-4 text-center transition-colors duration-300 md:w-auto"
            >
              <Icon name="fa6-brands:linkedin-in" class="mx-auto size-4" />
            </button>
          </div>
          <!-- 'or' divider -->
          <div class="flex-100 mt-8 flex items-center">
            <hr
              class="border-muted-200 dark:border-muted-700 flex-auto border-t-2"
            >
            <span
              class="text-muted-600 dark:text-muted-300 px-4 font-sans font-light"
            >
              OR
            </span>
            <hr
              class="border-muted-200 dark:border-muted-700 flex-auto border-t-2"
            >
          </div>
        </div>

        <!-- Form section -->
        <form
          method="POST"
          action=""
          class="mt-6"
          novalidate
          @submit.prevent="onSubmit"
        >
          <div class="mt-5">
            <div>
              <div class="space-y-4">
                <Field
                  v-slot="{ field, errorMessage, handleChange, handleBlur }"
                  name="username"
                >
                  <BaseInput
                    :model-value="field.value"
                    :error="errorMessage"
                    :disabled="isSubmitting"
                    type="text"
                    label="Username"
                    placeholder="Username"
                    autocomplete="username"
                    :classes="{
                      input: 'h-12',
                    }"
                    @update:model-value="handleChange"
                    @blur="handleBlur"
                  />
                </Field>
                <Field
                  v-slot="{ field, errorMessage, handleChange, handleBlur }"
                  name="email"
                >
                  <BaseInput
                    :model-value="field.value"
                    :error="errorMessage"
                    :disabled="isSubmitting"
                    type="email"
                    label="Email address"
                    placeholder="Email address"
                    autocomplete="email"
                    :classes="{
                      input: 'h-12',
                    }"
                    @update:model-value="handleChange"
                    @blur="handleBlur"
                  />
                </Field>
                <Field
                  v-slot="{ field, errorMessage, handleChange, handleBlur }"
                  name="password"
                >
                  <AddonInputPassword
                    ref="passwordRef"
                    :model-value="field.value"
                    :error="errorMessage"
                    :disabled="isSubmitting"
                    :user-inputs="[values.username ?? '', values.email ?? '']"
                    label="Password"
                    placeholder="Password"
                    :classes="{
                      wrapper: 'relative',
                      input: 'h-12',
                    }"
                    @update:model-value="handleChange"
                    @blur="handleBlur"
                  />
                </Field>
                <Field
                  v-slot="{ field, errorMessage, handleChange, handleBlur }"
                  name="confirmPassword"
                >
                  <BaseInput
                    :model-value="field.value"
                    :error="errorMessage"
                    :disabled="isSubmitting"
                    type="password"
                    label="Confirm password"
                    placeholder="Confirm password"
                    autocomplete="new-password"
                    :classes="{
                      input: 'h-12',
                    }"
                    @update:model-value="handleChange"
                    @blur="handleBlur"
                  />
                </Field>
              </div>

              <!-- Submit -->
              <div class="mt-6">
                <div class="block w-full rounded-md shadow-sm">
                  <BaseButton
                    :disabled="isSubmitting"
                    :loading="isSubmitting"
                    type="submit"
                    color="primary"
                    class="!h-11 w-full"
                  >
                    Create account
                  </BaseButton>
                </div>
              </div>
            </div>

            <!-- Already have account link -->
            <p
              class="text-muted-400 mt-4 flex justify-between font-sans text-xs leading-5"
            >
              <span>Already have an account?</span>
              <NuxtLink
                to="/auth/login"
                class="text-primary-600 hover:text-primary-500 font-medium underline-offset-4 transition duration-150 ease-in-out hover:underline"
              >
                Sign in here
              </NuxtLink>
            </p>
          </div>
        </form>
      </div>
    </div>

    <div
      class="from-primary-900 to-primary-500 i group relative hidden w-1/2 items-center justify-around overflow-hidden bg-gradient-to-tr md:flex"
    >
      <div class="mx-auto max-w-xs text-center">
        <BaseHeading as="h2" size="3xl" weight="medium" class="text-white">
          Join thousands of users
        </BaseHeading>
        <BaseParagraph size="sm" class="text-muted-200 mb-3">
          Create your account and start building amazing projects with our platform
        </BaseParagraph>
        <BaseButton to="/auth/login" rounded="lg" class="w-full">
          Already have an account?
        </BaseButton>
      </div>
      <div
        class="bg-muted-200/20 absolute -start-6 -top-6 h-14 w-0 origin-top-left rotate-45 rounded-full transition-all delay-[25ms] duration-300 group-hover:w-72"
      />
      <div
        class="bg-muted-200/20 absolute -top-12 start-20 h-14 w-0 origin-top-left rotate-45 rounded-full transition-all delay-75 duration-300 group-hover:w-48"
      />
      <div
        class="bg-muted-200/20 absolute -start-7 top-24 h-14 w-0 origin-top-left rotate-45 rounded-full transition-all delay-150 duration-300 group-hover:w-40"
      />

      <div
        class="bg-muted-200/20 absolute -bottom-6 -end-6 h-14 w-0 origin-bottom-right rotate-45 rounded-full transition-all delay-150 duration-300 group-hover:w-72"
      />
      <div
        class="bg-muted-200/20 absolute -bottom-12 end-20 h-14 w-0 origin-bottom-right rotate-45 rounded-full transition-all delay-75 duration-300 group-hover:w-48"
      />
      <div
        class="bg-muted-200/20 absolute -end-7 bottom-24 h-14 w-0 origin-bottom-right rotate-45 rounded-full transition-all delay-[25ms] duration-300 group-hover:w-40"
      />
    </div>
  </div>
</template>
