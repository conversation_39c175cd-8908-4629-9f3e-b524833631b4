import type { FirebaseApp } from 'firebase/app'
import type { Auth } from 'firebase/auth'
import type { Firestore } from 'firebase/firestore'
import { initializeServerApp } from 'firebase/app'
import {

  browserSessionPersistence,
  connectAuthEmulator,
  getAuth,
  onAuthStateChanged,
  setPersistence,
} from 'firebase/auth'
import { connectFirestoreEmulator, doc, getFirestore, setDoc } from 'firebase/firestore'
import { getGenerativeModel, getVertexAI } from 'firebase/vertexai'

let firebaseApp: FirebaseApp | null = null
let firestoreInstance: Firestore | null = null
let authInstance: Auth | null = null
let isEmulatorConnected = false

export async function useFirebaseServer(authIdToken: string | undefined) {
  const config = useRuntimeConfig()
  const isDevelopment = process.env.NODE_ENV === 'development'
  const firebaseConfig: any = config.public.firebase

  // Enhanced logging for debugging
  console.log('Initializing Firebase Server with token:', authIdToken ? 'Token provided' : 'No token')
  console.log('Environment:', isDevelopment ? 'Development' : 'Production')

  try {
    // Initialize Firebase app only if not already initialized
    if (!firebaseApp) {
      console.log('Creating new Firebase app instance')
      firebaseApp = initializeServerApp(firebaseConfig, {
        authIdToken: authIdToken ?? undefined,
      })
    }
    else {
      console.log('Using existing Firebase app instance')
    }

    // Get or create Auth instance
    if (!authInstance) {
      console.log('Creating new Auth instance')
      authInstance = getAuth(firebaseApp)
    }
    else {
      console.log('Using existing Auth instance')
    }

    // Get or create Firestore instance
    if (!firestoreInstance) {
      console.log('Creating new Firestore instance')
      firestoreInstance = getFirestore(firebaseApp)

      // Verify Firestore instance
      if (!firestoreInstance) {
        console.error('Failed to create Firestore instance')
        throw new Error('Failed to initialize Firestore')
      }
      else {
        console.log('Firestore instance created successfully')
      }
    }
    else {
      console.log('Using existing Firestore instance')
    }
    console.log('Emulator connection status:', isEmulatorConnected)
    // Connect to emulators only once in development
    if (isDevelopment && !isEmulatorConnected) {
      console.log('******----------********')
      try {
        connectFirestoreEmulator(firestoreInstance, 'localhost', 8080)

        const authUrl = 'http://127.0.0.1:9099'
        connectAuthEmulator(authInstance, authUrl, { disableWarnings: true })

        setPersistence(authInstance, browserSessionPersistence).catch((err) => {
          console.error('[Firebase] Error enabling persistence:', err)
        })

        isEmulatorConnected = true
        console.log('[Firebase] Emulator connection successful.')

        // Add a test write after connecting to the emulator
        try {
          console.log('[Firebase] Attempting emulator connection test write...')
          await setDoc(doc(firestoreInstance, 'emulator_test_writes', 'connection_test'), {
            timestamp: new Date().toISOString(),
            message: 'Emulator connection test write',
          })
          console.log('[Firebase] Emulator connection test write successful.')
        }
        catch (testError) {
          console.error('[Firebase] Emulator connection test write failed:', testError)
        }
      }
      catch (error) {
        console.error('[Firebase] Error setting up emulators:', error)
        // Continue without emulators in case of error
      }
    }
    console.log('isEmulatorConnected: ', isEmulatorConnected)

    // Initialize VertexAI for each request as it might need the current auth token
    const vertexAI = getVertexAI(firebaseApp)
    const model = getGenerativeModel(vertexAI, {
      model: 'gemini-1.5-flash-preview-0514',
    })

    console.log('[Firebase] useFirebaseServer returning:', {
      firestoreInstanceExists: !!firestoreInstance,
      isEmulatorConnected,
    })

    // Final verification before returning
    if (!firestoreInstance) {
      console.error('Firestore instance is null before return')
      throw new Error('Firestore initialization failed')
    }

    return {
      firebaseApp,
      firestore: firestoreInstance,
      auth: authInstance,
      vertexAI,
      model,
      storage: null,
      onAuthStateChanged,
      messaging: null,
    }
  }
  catch (error) {
    console.error('[Firebase] Error in useFirebaseServer:', error)
    throw error
  }
}
