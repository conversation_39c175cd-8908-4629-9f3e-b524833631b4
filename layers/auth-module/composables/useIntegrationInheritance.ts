import type { Integration } from '../types/integration'

export function useIntegrationInheritance() {
  const { currentUser, currentProfile, currentWorkspace } = useAuth()

  // Get effective integration for a provider considering inheritance
  const getEffectiveIntegration = (
    integrations: Integration[],
    provider: string,
  ): Integration | null => {
    if (!currentProfile.value) {
      // User context - just find active integration for provider
      return integrations.find(i =>
        i.provider === provider
        && i.isActive
        && !i.profileId,
      ) || null
    }

    // Profile context - check profile-specific first, then inherited
    const profileIntegration = integrations.find(i =>
      i.provider === provider
      && i.isActive
      && i.profileId === currentProfile.value?.id,
    )

    if (profileIntegration) {
      return profileIntegration
    }

    // Fall back to user-level integration if available to profiles
    const userIntegration = integrations.find(i =>
      i.provider === provider
      && i.isActive
      && !i.profileId
      && i.availableToProfiles,
    )

    return userIntegration || null
  }

  // Check if an integration is inherited (user-level shown in profile context)
  const isInherited = (integration: Integration): boolean => {
    if (!currentProfile.value)
      return false
    return !integration.profileId && integration.availableToProfiles
  }

  // Check if an integration is overridden (profile has its own)
  const isOverridden = (
    integrations: Integration[],
    userIntegration: Integration,
  ): boolean => {
    if (!currentProfile.value)
      return false

    return integrations.some(i =>
      i.provider === userIntegration.provider
      && i.profileId === currentProfile.value?.id
      && i.isActive,
    )
  }

  // Get inheritance status for an integration
  const getInheritanceStatus = (
    integration: Integration,
    allIntegrations: Integration[],
  ) => {
    const isProfileSpecific = !!integration.profileId
    const inherited = isInherited(integration)
    const overridden = !isProfileSpecific && isOverridden(allIntegrations, integration)

    return {
      isProfileSpecific,
      isInherited: inherited,
      isOverridden: overridden,
      isEffective: !overridden, // Is this the integration that will be used?
      source: isProfileSpecific ? 'profile' : 'user' as 'profile' | 'user',
    }
  }

  // Group integrations by provider with inheritance info
  const groupIntegrationsByProvider = (integrations: Integration[]) => {
    const grouped = new Map<string, {
      provider: string
      userIntegration?: Integration
      profileIntegration?: Integration
      effective?: Integration
      hasOverride: boolean
    }>()

    integrations.forEach((integration) => {
      const provider = integration.provider
      let group = grouped.get(provider)

      if (!group) {
        group = {
          provider,
          hasOverride: false,
        }
        grouped.set(provider, group)
      }

      if (integration.profileId === currentProfile.value?.id) {
        group.profileIntegration = integration
        group.hasOverride = true
      }
      else if (!integration.profileId) {
        group.userIntegration = integration
      }
    })

    // Determine effective integration for each provider
    grouped.forEach((group) => {
      group.effective = getEffectiveIntegration(integrations, group.provider)
    })

    return Array.from(grouped.values())
  }

  // Get summary of inheritance for current context
  const getInheritanceSummary = (integrations: Integration[]) => {
    const summary = {
      totalIntegrations: integrations.length,
      userIntegrations: 0,
      profileIntegrations: 0,
      inheritedIntegrations: 0,
      overriddenIntegrations: 0,
    }

    integrations.forEach((integration) => {
      if (integration.profileId) {
        summary.profileIntegrations++
      }
      else {
        summary.userIntegrations++
        if (currentProfile.value && integration.availableToProfiles) {
          summary.inheritedIntegrations++
          if (isOverridden(integrations, integration)) {
            summary.overriddenIntegrations++
          }
        }
      }
    })

    return summary
  }

  // Helper to determine if we should show inheritance UI
  const shouldShowInheritanceUI = computed(() => {
    return !!currentProfile.value
  })

  // Helper to get inheritance label for UI
  const getInheritanceLabel = (integration: Integration, allIntegrations: Integration[]): string => {
    if (!currentProfile.value)
      return ''

    const status = getInheritanceStatus(integration, allIntegrations)

    if (status.isProfileSpecific) {
      return 'Profile-specific'
    }
    else if (status.isInherited && status.isOverridden) {
      return 'Inherited (overridden)'
    }
    else if (status.isInherited) {
      return 'Inherited from user'
    }

    return 'User-level'
  }

  // Helper to get inheritance icon
  const getInheritanceIcon = (integration: Integration, allIntegrations: Integration[]): string => {
    const status = getInheritanceStatus(integration, allIntegrations)

    if (status.isProfileSpecific) {
      return 'ph:user-circle'
    }
    else if (status.isInherited && status.isOverridden) {
      return 'ph:link-break'
    }
    else if (status.isInherited) {
      return 'ph:link'
    }

    return 'ph:users'
  }

  return {
    getEffectiveIntegration,
    isInherited,
    isOverridden,
    getInheritanceStatus,
    groupIntegrationsByProvider,
    getInheritanceSummary,
    shouldShowInheritanceUI,
    getInheritanceLabel,
    getInheritanceIcon,
  }
}
