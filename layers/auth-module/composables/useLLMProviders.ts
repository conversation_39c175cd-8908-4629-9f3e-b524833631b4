import type { LLMModel, LLMProviderConfig } from '../types/integration'
import { getAll<PERSON><PERSON><PERSON><PERSON>s, getLL<PERSON><PERSON><PERSON>, getProviderModels } from '../config/llm-providers'

export function useLLMProviders() {
  // State for dynamic models (fetched from API)
  const dynamicModels = ref<Record<string, LLMModel[]>>({})
  const modelsLoading = ref(false)
  const modelsError = ref<string | null>(null)

  // Get all available providers
  const providers = computed(() => getAllLLMProviders())

  // Get a specific provider
  const getProvider = (providerId: string): LLMProviderConfig | undefined => {
    return getLLMProvider(providerId)
  }

  // Get models for a provider (with dynamic fallback)
  const getModels = (providerId: string): LLMModel[] => {
    // Check if we have dynamic models
    if (dynamicModels.value[providerId]) {
      return dynamicModels.value[providerId]
    }

    // Fall back to static models
    return getProviderModels(providerId)
  }

  // Fetch latest models from API
  const fetchLatestModels = async (providerId?: string) => {
    try {
      modelsLoading.value = true
      modelsError.value = null

      const params = providerId ? `?provider=${providerId}` : ''
      const response = await $fetch(`/api/integrations/models${params}`)

      if (response.success) {
        if (providerId && response.models) {
          // Update specific provider
          dynamicModels.value[providerId] = response.models
        }
        else if (response.providers) {
          // Update all providers
          response.providers.forEach((provider: any) => {
            dynamicModels.value[provider.id] = provider.models
          })
        }
      }
    }
    catch (error) {
      console.error('Failed to fetch models:', error)
      modelsError.value = (error as Error).message
    }
    finally {
      modelsLoading.value = false
    }
  }

  // Get provider icon component props
  const getProviderIcon = (providerId: string) => {
    const provider = getProvider(providerId)
    return provider?.icon || 'ph:robot'
  }

  // Get provider display name
  const getProviderName = (providerId: string) => {
    const provider = getProvider(providerId)
    return provider?.name || providerId
  }

  // Get model by ID
  const getModel = (providerId: string, modelId: string): LLMModel | undefined => {
    const models = getModels(providerId)
    return models.find(m => m.id === modelId)
  }

  // Get default model for a provider
  const getDefaultModel = (providerId: string): LLMModel | undefined => {
    const models = getModels(providerId)
    return models[0] // First model is default
  }

  // Format model context window for display
  const formatContextWindow = (tokens: number): string => {
    if (tokens >= 1000000) {
      return `${(tokens / 1000000).toFixed(1)}M`
    }
    else if (tokens >= 1000) {
      return `${(tokens / 1000).toFixed(0)}K`
    }
    return tokens.toString()
  }

  // Format model pricing for display
  const formatPricing = (model: LLMModel): string => {
    if (!model.pricing)
      return 'Pricing not available'

    const { input, output, currency } = model.pricing

    if (input === 0 && output === 0) {
      return 'Free'
    }

    return `$${input}/$${output} per 1M tokens`
  }

  // Check if a model supports a specific capability
  const modelSupports = (model: LLMModel, capability: string): boolean => {
    return model.capabilities.includes(capability)
  }

  // Group models by capability
  const getModelsByCapability = (providerId: string, capability: string): LLMModel[] => {
    const models = getModels(providerId)
    return models.filter(m => modelSupports(m, capability))
  }

  // Sort models by various criteria
  const sortModels = (models: LLMModel[], criteria: 'name' | 'context' | 'price' = 'name'): LLMModel[] => {
    const sorted = [...models]

    switch (criteria) {
      case 'name':
        return sorted.sort((a, b) => a.displayName.localeCompare(b.displayName))

      case 'context':
        return sorted.sort((a, b) => b.contextWindow - a.contextWindow)

      case 'price':
        return sorted.sort((a, b) => {
          const priceA = a.pricing?.input || Infinity
          const priceB = b.pricing?.input || Infinity
          return priceA - priceB
        })

      default:
        return sorted
    }
  }

  // Filter out deprecated models
  const getActiveModels = (providerId: string): LLMModel[] => {
    const models = getModels(providerId)
    return models.filter(m => !m.deprecated)
  }

  // Load dynamic models on mount
  onMounted(() => {
    // Fetch latest models in background (don't block UI)
    fetchLatestModels().catch(console.error)
  })

  return {
    providers,
    dynamicModels: readonly(dynamicModels),
    modelsLoading: readonly(modelsLoading),
    modelsError: readonly(modelsError),
    getProvider,
    getModels,
    getModel,
    getDefaultModel,
    getProviderIcon,
    getProviderName,
    fetchLatestModels,
    formatContextWindow,
    formatPricing,
    modelSupports,
    getModelsByCapability,
    sortModels,
    getActiveModels,
  }
}
