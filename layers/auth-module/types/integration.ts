import type { Timestamp } from 'firebase/firestore'

export type LLMProvider = 'openai' | 'anthropic' | 'google' | 'xai' | 'mistral' | 'cohere' | 'perplexity' | 'meta' | 'nvidia' | 'groq'

export interface Integration {
  id: string
  userId: string
  workspaceId?: string // Optional for profile-specific integrations
  profileId?: string // Optional for profile-specific integrations

  provider: LLMProvider
  name: string // User-friendly name
  description?: string

  // Encrypted credentials
  credentials: {
    apiKey: string // Encrypted
    encryptedAt: Timestamp
  }

  // Provider-specific settings
  settings: {
    defaultModel?: string
    baseUrl?: string // For custom endpoints
    maxTokens?: number
    temperature?: number
  }

  // Status
  isActive: boolean
  isDefault: boolean // Default for this provider
  availableToProfiles: boolean // User-level integrations available to profiles

  // Metadata
  createdAt: Timestamp
  updatedAt: Timestamp
  lastUsedAt?: Timestamp
  deletedAt?: Timestamp | null
}

export interface LLMProviderConfig {
  id: LLMProvider
  name: string
  icon: string
  description: string
  models: LLMModel[]
  requiredCredentials: string[]
  endpoints: {
    base: string
    chat?: string
    embeddings?: string
    models?: string // For fetching available models
  }
  headers?: Record<string, string>
}

export interface LLMModel {
  id: string
  name: string
  displayName: string
  contextWindow: number
  capabilities: string[]
  pricing?: {
    input: number // Per million tokens
    output: number // Per million tokens
    currency: string
  }
  deprecated?: boolean
  releaseDate?: string
  knowledgeCutoff?: string
}

export interface IntegrationFormData {
  provider: LLMProvider
  name: string
  description?: string
  apiKey: string
  settings?: {
    defaultModel?: string
    baseUrl?: string
    maxTokens?: number
    temperature?: number
  }
  availableToProfiles?: boolean
}

export interface IntegrationValidationResponse {
  isValid: boolean
  error?: string
  models?: LLMModel[]
  accountInfo?: {
    organizationName?: string
    usageLimits?: Record<string, any>
  }
}

export interface UseIntegrationsOptions {
  userId?: string
  workspaceId?: string
  profileId?: string
  includeInherited?: boolean
}
