{"permissions": {"allow": ["Bash(find:*)", "Bash(ls:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(cp:*)", "<PERSON><PERSON>(touch:*)", "Bash(rm:*)", "mcp__zen__planner", "Bash(grep:*)", "<PERSON><PERSON>(diff:*)", "Bash(pnpm lint:*)", "Bash(npx eslint:*)", "Bash(pnpm install:*)", "<PERSON><PERSON>(chmod:*)", "mcp__zen__debug", "Bash(pnpm why:*)", "Bash(pnpm list:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "Bash(node:*)", "<PERSON><PERSON>(mv:*)", "Bash(pnpm dev:*)", "Bash(git init:*)", "Bash(git remote add:*)", "Bash(git add .)", "Bash(git commit:*)", "<PERSON><PERSON>(git push -f origin main)", "Bash(git add:*)", "Bash(git reset:*)", "Bash(git push:*)", "mcp__zen__thinkdeep", "Bash(pnpm --filter=pib dev)", "Bash(firebase deploy:*)", "<PERSON><PERSON>(firebase login:*)", "<PERSON><PERSON>(curl:*)", "Bash(awk:*)", "<PERSON><PERSON>(sed:*)", "Bash(pnpm exec eslint:*)", "Bash(pnpm run lint:*)", "Bash(pnpm -w run lint 2 >& 1)", "mcp__ide__getDiagnostics", "Bash(pnpm typecheck:*)", "Bash(pnpm run:*)", "<PERSON><PERSON>(timeout 10 pnpm dev)", "Bash(timeout 10 pnpm --filter=pib dev)", "Bash(pnpm add:*)", "WebFetch(domain:tailwindcss.com)", "Bash(npx:*)", "Bash(rg:*)", "WebFetch(domain:github.com)", "Bash(pnpm test:unit:*)", "Bash(pnpm build)", "Bash(pnpm --filter=pib build)", "Bash(pnpm --filter=\"@cssninja/tairo-component-meta\" build)", "Bash(pnpm prepack:*)", "<PERSON><PERSON>(cat:*)", "Bash(pnpm test:*)", "Bash(git checkout:*)", "mcp__zen__version", "mcp__zen__codereview", "Bash(pnpm deploy:rules:*)", "Bash(pnpm:*)", "Bash(eslint --cache --fix .)", "Bash(open http://localhost:3001/user/integrations)", "Bash(pgrep:*)"], "deny": []}}